/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { useSocket } from "@/contexts/SocketContext";
import { Order, Payment } from "@/types/order";
import { playNotificationSound } from "@/utils/notificationSound";

export const useOrderSocket = (orderId?: string) => {
  const { customerSocket, adminSocket } = useSocket();
  const [order, setOrder] = useState<Order | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [socket, setSocket] = useState<any>(null);

  // Function to update orders optimistically
  const updateOrderOptimistically = (orderId: string, updates: any) => {
    setOrders((prev) =>
      prev.map((order) => {
        if (order._id === orderId) {
          const updatedOrder = { ...order };

          // Apply updates
          Object.keys(updates).forEach((key) => {
            if (key.includes(".")) {
              // Handle nested updates like "items.0.isServed"
              const [parentKey, index, childKey] = key.split(".");
              if (
                parentKey === "items" &&
                updatedOrder.items[parseInt(index)]
              ) {
                updatedOrder.items[parseInt(index)] = {
                  ...updatedOrder.items[parseInt(index)],
                  [childKey]: updates[key],
                };
              }
            } else {
              // Handle direct updates like "status"
              (updatedOrder as any)[key] = updates[key];
            }
          });

          return updatedOrder;
        }
        return order;
      })
    );
  };

  useEffect(() => {
    const isAdmin = !!localStorage.getItem("auth-token");
    console.log("isAdmin", localStorage.getItem("auth-token"));
    if (isAdmin) {
      setSocket(adminSocket);
    } else {
      setSocket(customerSocket);
    }

    if (!isAdmin && orderId && customerSocket) {
      setLoading(true);

      customerSocket.emit("join-order", orderId);

      customerSocket.on("connect", () => {
        console.log("Customer socket connected");
      });

      customerSocket.on("order-status-update", (data: { data: Order }) => {
        setOrder(data.data);
        setLoading(false);
      });

      // Listen for order items updates
      customerSocket.on(
        "order-items-update",
        (data: {
          type: string;
          data: {
            order: Order;
            updatedBy: string;
            timestamp: string;
          };
        }) => {
          console.log("Customer received order items update:", data);
          if (data.data.order) {
            setOrder(data.data.order);

            // Show notification if updated by admin
            if (data.data.updatedBy === "admin") {
              // Play notification sound
              playNotificationSound("order-update");

              // Show browser notification if permission granted
              if (
                window.Notification &&
                Notification.permission === "granted"
              ) {
                new Notification(
                  `Order #${data.data.order.orderNumber} Updated`,
                  {
                    body: `Restaurant has modified your order`,
                    icon: "/favicon.ico",
                    tag: `order-items-update-${data.data.order._id}`,
                  }
                );
              }
            }
          }
        }
      );

      // Listen for payment updates
      customerSocket.on(
        "payment-update",
        (data: { type: string; data: { order: Order; payment: Payment } }) => {
          if (data.data.order) {
            setOrder(data.data.order);
          }
        }
      );

      return () => {
        if (customerSocket) {
          customerSocket.emit("leave-order", orderId);
          customerSocket.off("order-status-update");
          customerSocket.off("order-items-update");
          customerSocket.off("payment-update");
          customerSocket.off("connect");
        }
      };
    }

    if (isAdmin && adminSocket) {
      console.log("Admin socket connected");
      setLoading(true);

      adminSocket.on("connect", () => {
        console.log("Admin socket connected event");
        // Force refresh of orders when socket connects
        adminSocket.emit("refresh-orders");
      });

      // Listen for initial orders
      adminSocket.on("initial-orders", (data: { orders: Order[] }) => {
        console.log("Admin received initial orders:", data);
        if (data && data.orders) {
          setOrders(data.orders);
          setLoading(false);
        } else {
          console.error("Received invalid initial orders data:", data);
        }
      });

      // Listen for new orders
      adminSocket.on("new-order", (data: { type: string; data: Order }) => {
        console.log("Admin received new order:", data);
        if (data && data.data) {
          // Add the new order to the beginning of the list
          setOrders((prev) => [data.data, ...prev]);

          // Play notification sound
          playNotificationSound("new-order");

          // Show a notification
          if (window.Notification && Notification.permission === "granted") {
            new Notification("New Order", {
              body: `New order #${data.data.orderNumber} received`,
            });
          }
        } else {
          console.error("Received invalid new order data:", data);
        }
      });

      // Listen for order updates
      adminSocket.on("order-status-update", (data: { data: Order }) => {
        console.log("Admin received order update:", data);
        setOrders((prev) =>
          prev.map((order) => (order._id === data.data._id ? data.data : order))
        );
      });

      // Listen for order items updates
      adminSocket.on(
        "order-items-update",
        (data: {
          type: string;
          data: {
            order: Order;
            updatedBy: string;
            timestamp: string;
          };
        }) => {
          console.log("🔄 Admin received order items update:", {
            orderId: data.data.order._id,
            updatedBy: data.data.updatedBy,
            itemsCount: data.data.order.items.length,
            totalAmount: data.data.order.totalAmount,
            finalAmount: data.data.order.finalAmount,
            timestamp: data.data.timestamp,
          });

          if (data.data.order) {
            setOrders((prev) => {
              const orderExists = prev.find(
                (o) => o._id === data.data.order._id
              );

              if (!orderExists) {
                console.log(
                  "⚠️ Order not found in current admin orders list, adding it"
                );
                // If order doesn't exist in current list, add it
                return [data.data.order, ...prev];
              }

              // Log the before and after comparison
              const oldOrder = orderExists;
              const newOrder = data.data.order;

              console.log("📊 Order comparison:", {
                orderId: newOrder._id,
                oldItemsCount: oldOrder.items.length,
                newItemsCount: newOrder.items.length,
                oldTotal: oldOrder.totalAmount,
                newTotal: newOrder.totalAmount,
                oldFinalAmount: oldOrder.finalAmount,
                newFinalAmount: newOrder.finalAmount,
                updatedBy: data.data.updatedBy,
                oldItems: oldOrder.items.map((item) => ({
                  dishName: item.dishId?.name || item.dishName,
                  quantity: item.quantity,
                  isServed: item.isServed,
                })),
                newItems: newOrder.items.map((item) => ({
                  dishName: item.dishId?.name || item.dishName,
                  quantity: item.quantity,
                  isServed: item.isServed,
                })),
              });

              console.log(
                "✅ Updating order in admin list:",
                data.data.order._id
              );
              const updatedOrders = prev.map((order) =>
                order._id === data.data.order._id ? data.data.order : order
              );

              console.log(
                "📋 Orders list updated, new count:",
                updatedOrders.length
              );
              return updatedOrders;
            });

            // Show notification if updated by user
            if (data.data.updatedBy === "user") {
              // Play notification sound
              playNotificationSound("order-update");

              // Show browser notification if permission granted
              if (
                window.Notification &&
                Notification.permission === "granted"
              ) {
                new Notification(
                  `Order #${data.data.order.orderNumber} Updated`,
                  {
                    body: `Customer has modified their order items`,
                    icon: "/favicon.ico",
                    tag: `order-items-update-${data.data.order._id}`,
                  }
                );
              }
            }
          }
        }
      );

      // Listen for payment updates
      adminSocket.on(
        "payment-update",
        (data: { type: string; data: { order: Order; payment: Payment } }) => {
          console.log("Admin received payment update:", data);
          if (data.data.order) {
            setOrders((prev) =>
              prev.map((order) =>
                order._id === data.data.order._id ? data.data.order : order
              )
            );
          }
        }
      );

      // Listen for order update notifications (when customers update orders)
      adminSocket.on(
        "order-update-notification",
        (data: {
          type: string;
          data: {
            orderId: string;
            orderNumber: string;
            customerName: string;
            outletName: string;
            totalAmount: number;
            timestamp: string;
            message: string;
          };
        }) => {
          console.log("Admin received order update notification:", data);
          // This is handled by the OrderUpdateNotifications component
          // but we can also trigger a refresh here if needed
        }
      );

      // Listen for admin order update notifications (when other admins update orders)
      adminSocket.on(
        "admin-order-update-notification",
        (data: {
          type: string;
          data: {
            orderId: string;
            orderNumber: string;
            customerName: string;
            outletName: string;
            totalAmount: number;
            timestamp: string;
            message: string;
          };
        }) => {
          console.log("Admin received admin order update notification:", data);
          // This notification is for when other admins update orders
          // The actual order data is already updated via order-items-update event
        }
      );

      return () => {
        if (adminSocket) {
          adminSocket.off("initial-orders");
          adminSocket.off("new-order");
          adminSocket.off("order-status-update");
          adminSocket.off("order-items-update");
          adminSocket.off("payment-update");
          adminSocket.off("order-update-notification");
          adminSocket.off("admin-order-update-notification");
          adminSocket.off("connect");
        }
      };
    }
  }, [orderId, customerSocket, adminSocket]);

  return {
    order,
    orders,
    loading,
    socket,
    updateOrderOptimistically,
  };
};
