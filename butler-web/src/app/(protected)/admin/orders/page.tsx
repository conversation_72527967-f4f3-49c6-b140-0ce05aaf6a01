/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useOrderSocket } from "@/hooks/useOrderSocket";
import { useState, useEffect } from "react";
import { getAllOutlets, getAllEmployees } from "@/server/admin";
import { Outlet, User } from "@/app/type";
import { toast } from "sonner";
import PaymentRequestButton from "@/components/payment/PaymentRequestButton";
import PaymentLinkDisplay from "@/components/payment/PaymentLinkDisplay";
import { Payment, Order } from "@/types/order";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import OrderManagementDialog from "@/components/custom/orders/OrderManagementDialog";
import CreateOrderDialog from "@/components/custom/orders/CreateOrderDialog";
import AdminOrderUpdateDialog from "@/components/admin/AdminOrderUpdateDialog";
import OrderUpdateNotifications from "@/components/admin/OrderUpdateNotifications";
import {
  Clock,
  AlertCircle,
  ChefHat,
  PlusCircle,
  Trash2,
  FileDown,
  Phone,
  User as UserIcon,
  Check,
  X,
  CheckCircle,
  Mail,
  ChevronDown,
  Edit,
} from "lucide-react";
import { downloadOrderBill, emailOrderInvoice } from "@/utils/pdfGenerator";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  deleteOrder,
  updateOrderPaymentStatus,
  markDishAsServed,
} from "@/server/admin";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import OrderSearch from "@/components/admin/OrderSearch";
import { useRouter, useSearchParams } from "next/navigation";

const orderStatuses = [
  { status: "pending", order: 1 },
  { status: "preparing", order: 2 },
  { status: "ready", order: 3 },
  { status: "completed", order: 4 },
];

const AdminOrdersPage = () => {
  const {
    orders: socketOrders,
    loading: socketLoading,
    socket,
    updateOrderOptimistically,
    refreshOrders,
  } = useOrderSocket();
  const router = useRouter();
  const params = useSearchParams();
  const initialStatus = params?.get("status") || "pending";
  const [selectedStatus, setSelectedStatus] = useState<string>(initialStatus);
  const [selectedOutlet, setSelectedOutlet] = useState<string>("all");
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [payments, setPayments] = useState<Record<string, Payment>>({});
  const [staff, setStaff] = useState<User[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderManagement, setShowOrderManagement] =
    useState<boolean>(false);
  const [showCreateOrder, setShowCreateOrder] = useState<boolean>(false);
  const [showOrderUpdate, setShowOrderUpdate] = useState<boolean>(false);
  const [orderToUpdate, setOrderToUpdate] = useState<Order | null>(null);
  const [highlightedOrderId, setHighlightedOrderId] = useState<string | null>(
    null
  );
  const [updatingDishes, setUpdatingDishes] = useState<Set<string>>(new Set());
  const [emailingInvoices, setEmailingInvoices] = useState<Set<string>>(
    new Set()
  );

  // Pagination state
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    hasNext: false,
    total: 0,
  });
  const [loadingMore, setLoadingMore] = useState(false);
  const [useSocketOrders, setUseSocketOrders] = useState(true);

  // Fetch orders from API with pagination
  const fetchOrdersFromAPI = async (page = 1, append = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/paginated?page=${page}&limit=${pagination.limit}&status=${selectedStatus}&outlet=${selectedOutlet}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();

      if (data.success) {
        if (append) {
          setAllOrders((prev) => [...prev, ...data.data]);
        } else {
          setAllOrders(data.data);
        }
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to load orders");
    } finally {
      setLoadingMore(false);
    }
  };

  // Load more orders
  const loadMoreOrders = () => {
    if (pagination.hasNext && !loadingMore) {
      fetchOrdersFromAPI(pagination.page + 1, true);
    }
  };

  // Get current orders based on mode
  const orders = useSocketOrders ? socketOrders : allOrders;
  const loading = useSocketOrders ? socketLoading : false;

  // Debug: Log when orders change
  useEffect(() => {
    console.log("📋 Admin orders updated:", {
      count: orders.length,
      useSocketOrders,
      socketOrdersCount: socketOrders.length,
      allOrdersCount: allOrders.length,
    });
  }, [orders.length, useSocketOrders, socketOrders.length, allOrders.length]);

  // Handle email invoice
  const handleEmailInvoice = async (order: Order) => {
    if (!order.userId.email) {
      toast.error("Customer email not available");
      return;
    }

    setEmailingInvoices((prev) => new Set(prev).add(order._id));

    try {
      await emailOrderInvoice(order);
      toast.success(`Invoice emailed to ${order.userId.email}`);
    } catch (error) {
      console.error("Error sending email invoice:", error);
      toast.error("Failed to send invoice email");
    } finally {
      setEmailingInvoices((prev) => {
        const newSet = new Set(prev);
        newSet.delete(order._id);
        return newSet;
      });
    }
  };

  // Handle dish serving status changes
  const handleDishServingToggle = async (
    orderId: string,
    itemIndex: number,
    currentStatus: boolean
  ) => {
    const dishKey = `${orderId}-${itemIndex}`;
    const newStatus = !currentStatus;
    setUpdatingDishes((prev) => new Set(prev).add(dishKey));

    try {
      // Optimistic update - immediately update the UI
      if (useSocketOrders && updateOrderOptimistically) {
        // For socket orders, use optimistic update function
        updateOrderOptimistically(orderId, {
          [`items.${itemIndex}.isServed`]: newStatus,
          [`items.${itemIndex}.servedAt`]: newStatus
            ? new Date().toISOString()
            : null,
        });
      } else {
        // For API orders, update local state
        setAllOrders((prevOrders) =>
          prevOrders.map((order) => {
            if (order._id === orderId) {
              const updatedItems = [...order.items];
              updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                isServed: newStatus,
                servedAt: newStatus ? new Date().toISOString() : undefined,
              };
              return { ...order, items: updatedItems };
            }
            return order;
          })
        );
      }

      const response = await markDishAsServed(orderId, itemIndex, newStatus);
      if (response.success) {
        toast.success(
          newStatus ? "Dish marked as served" : "Dish marked as not served"
        );
        // The socket will provide the authoritative update
      } else {
        toast.error("Failed to update dish status");
        // Revert optimistic update on error
        if (socket) {
          socket.emit("refresh-orders");
        }
      }
    } catch (error) {
      console.error("Error updating dish status:", error);
      toast.error("Failed to update dish status");
      // Revert optimistic update on error
      if (socket) {
        socket.emit("refresh-orders");
      }
    } finally {
      setUpdatingDishes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(dishKey);
        return newSet;
      });
    }
  };

  const getOrderStatusNumber = (status: string) => {
    switch (status) {
      case "pending":
        return 1;
      case "preparing":
        return 2;
      case "ready":
        return 3;
      case "completed":
        return 4;
      default:
        return 0;
    }
  };

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Fetch outlets
        const outletsResponse = await getAllOutlets();
        setOutlets(outletsResponse.data);

        // Fetch staff
        const staffResponse = await getAllEmployees();
        if (staffResponse.success) {
          setStaff(staffResponse.data);
        } else {
          toast.error("Failed to fetch staff");
        }

        // Try to use API for pagination, fallback to WebSocket
        if (!useSocketOrders) {
          fetchOrdersFromAPI(1, false);
        } else {
          // We're now relying solely on WebSockets for order data
          // The API endpoint for orders is not available
          if (loading) {
            console.log("Waiting for WebSocket to provide order data...");
            // After 5 seconds, if still loading, show a message and try to refresh
            setTimeout(() => {
              if (orders.length === 0 && socket) {
                console.log(
                  "No orders received from WebSocket yet, requesting refresh"
                );
                socket.emit("refresh-orders");
              }
            }, 5000);
          }
        }
      } catch (error) {
        console.error("Error fetching initial data:", error);
      }
    };

    fetchInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedStatus, selectedOutlet, useSocketOrders]);

  // Separate effect for socket orders
  useEffect(() => {
    if (useSocketOrders && socketOrders.length > 0) {
      // When using socket orders, check if we need to switch to API for pagination
      if (socketOrders.length >= 50) {
        // If we have 50 orders (the socket limit), switch to API mode for pagination
        setUseSocketOrders(false);
        setAllOrders(socketOrders);
        setPagination((prev) => ({
          ...prev,
          hasNext: true,
          total: socketOrders.length,
        }));
      }
    }
  }, [socketOrders, useSocketOrders]);

  const formatOutletDisplay = (outlet: Outlet) => {
    return `${outlet.name} (${outlet.address.split(",")[0]})`;
  };

  const filteredOrders = orders.filter(
    (order) =>
      (selectedStatus === "all" || order.status === selectedStatus) &&
      (selectedOutlet === "all" || order.outletId._id === selectedOutlet)
  );

  useEffect(() => {
    if (orders.length > 0) {
      // Fetch payment information for all orders
      orders.forEach(async (order) => {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${order._id}/payment`,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
              },
            }
          );

          const data = await response.json();
          if (data.success && data.data.payment) {
            setPayments((prev) => ({
              ...prev,
              [order._id]: data.data.payment,
            }));
          }
        } catch (error) {
          console.error("Error fetching payment details:", error);
        }
      });
    }
  }, [orders]);

  // Socket is already defined above

  useEffect(() => {
    if (socket) {
      socket.on(
        "payment-update",
        (data: { type: string; data: { order: any; payment: Payment } }) => {
          console.log("Admin received payment update:", data);

          // Update the payment information
          if (data.data.payment) {
            setPayments((prev) => ({
              ...prev,
              [data.data.order._id]: data.data.payment,
            }));
          }

          // Update the order information (including payment status)
          if (data.data.order) {
            if (useSocketOrders && updateOrderOptimistically) {
              // For socket orders, use optimistic update function
              updateOrderOptimistically(data.data.order._id, {
                paymentStatus: data.data.order.paymentStatus,
                paymentMethod: data.data.order.paymentMethod,
              });
            } else {
              // For API orders, update local state
              setAllOrders((prevOrders) =>
                prevOrders.map((order) =>
                  order._id === data.data.order._id
                    ? {
                        ...order,
                        paymentStatus: data.data.order.paymentStatus,
                        paymentMethod: data.data.order.paymentMethod,
                      }
                    : order
                )
              );
            }
            toast.success(
              `Payment status updated to ${data.data.order.paymentStatus}`
            );
          }
        }
      );

      return () => {
        socket.off("payment-update");
      };
    }
  }, [socket, useSocketOrders, updateOrderOptimistically]);

  const handlePaymentRequested = (orderId: string, payment: Payment) => {
    console.log(`Payment requested for order ID: ${orderId}`, payment);
    // Ensure orderId is a string
    const orderIdStr = orderId.toString();
    setPayments((prev) => ({
      ...prev,
      [orderIdStr]: payment,
    }));
  };

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      // Optimistic update - immediately update the UI
      if (useSocketOrders && updateOrderOptimistically) {
        // For socket orders, use optimistic update function
        updateOrderOptimistically(orderId, { status: newStatus });
      } else {
        // For API orders, update local state
        setAllOrders((prevOrders) =>
          prevOrders.map((order) =>
            order._id === orderId
              ? { ...order, status: newStatus as Order["status"] }
              : order
          )
        );
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${orderId}/status`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      const result = await response.json();
      if (result.success) {
        toast.success("Order status updated successfully");
        // The socket will provide the authoritative update
      } else {
        throw new Error(result.message || "Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");

      // Revert optimistic update on error
      if (socket) {
        socket.emit("refresh-orders");
      }
    }
  };

  const handleDeleteOrder = async (orderId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this order? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await deleteOrder(orderId);
      if (response.success) {
        toast.success("Order deleted successfully");
        // Remove the order from the local state
        // The socket will handle updating the orders list
        if (socket) {
          socket.emit("refresh-orders");
        }
      } else {
        toast.error(response.message || "Failed to delete order");
      }
    } catch (error) {
      console.error("Error deleting order:", error);
      toast.error("An error occurred while deleting the order");
    }
  };

  const handleNavigateToOrder = (orderId: string) => {
    // Find the order in the current list
    const order = orders.find((o) => o._id === orderId);

    if (order) {
      // Check if order is visible in current filtered view
      const isOrderVisible = filteredOrders.some((o) => o._id === orderId);

      if (isOrderVisible) {
        // Highlight the order temporarily
        setHighlightedOrderId(orderId);

        // Scroll to the order if it's visible
        const orderElement = document.getElementById(`order-${orderId}`);
        if (orderElement) {
          orderElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });

          // Remove highlight after 3 seconds
          setTimeout(() => {
            setHighlightedOrderId(null);
          }, 3000);
        }
      } else {
        // Order exists but is filtered out - switch to "All" view to show it
        toast.info(`Order #${order.orderNumber} updated`, {
          description: "Switching to 'All' view to show the updated order",
          action: {
            label: "View Order",
            onClick: () => {
              // Switch to "All" status and "All" outlets to ensure order is visible
              setSelectedStatus("all");
              setSelectedOutlet("all");

              // Update URL
              const url = new URL(window.location.href);
              url.searchParams.set("status", "all");
              router.push(url.pathname + url.search);

              // Highlight the order after a short delay to allow for state update
              setTimeout(() => {
                setHighlightedOrderId(orderId);
                const orderElement = document.getElementById(
                  `order-${orderId}`
                );
                if (orderElement) {
                  orderElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }

                // Remove highlight after 3 seconds
                setTimeout(() => {
                  setHighlightedOrderId(null);
                }, 3000);
              }, 100);
            },
          },
        });
      }
    } else {
      // Order not found in current view, suggest refresh
      toast.info("Order updated", {
        description:
          "The updated order may not be visible in current view. Click to refresh.",
        action: {
          label: "Refresh",
          onClick: () => window.location.reload(),
        },
      });
    }
  };

  if (loading) {
    return <div>Loading orders...</div>;
  }

  return (
    <div className="p-4">
      <div className="mb-4 flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <select
            value={selectedOutlet}
            onChange={(e) => setSelectedOutlet(e.target.value)}
            className="border p-2 rounded"
          >
            <option value="all">All Outlets</option>
            {outlets.map((outlet) => (
              <option key={outlet._id} value={outlet._id}>
                {formatOutletDisplay(outlet)}
              </option>
            ))}
          </select>

          <div className="flex gap-2">
            <Button
              onClick={() => {
                if (socket) {
                  socket.emit("refresh-orders");
                  toast.info("Refreshing orders...");
                }
              }}
              variant="outline"
              size="sm"
            >
              🔄 Refresh
            </Button>
            <Button
              onClick={() => setShowCreateOrder(true)}
              className="bg-primary text-white hover:bg-primary/90"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Create Order
            </Button>
          </div>
        </div>
        <Tabs
          defaultValue={selectedStatus}
          className="w-full"
          onValueChange={(e) => {
            const url = new URL(window.location.href);
            url.searchParams.set("status", e);
            router.push(url.pathname + url.search);
            setSelectedStatus(e);
          }}
        >
          <TabsList className="grid grid-cols-6 w-full sticky top-16">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="preparing">Preparing</TabsTrigger>
            <TabsTrigger value="ready">Ready</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            {/* <TabsTrigger value="cancelled">Cancelled</TabsTrigger> */}
          </TabsList>
        </Tabs>
      </div>

      <div className="space-y-4">
        {filteredOrders.map((order) => {
          return (
            <div
              key={order._id}
              id={`order-${order._id}`}
              className={`border-2 p-6 rounded-lg bg-white shadow-sm hover:shadow-lg transition-all duration-300 ${
                highlightedOrderId === order._id
                  ? "border-orange-400 bg-orange-50 shadow-lg ring-2 ring-orange-200"
                  : "border-gray-800"
              }`}
            >
              {/* Header Section */}
              <div className="flex justify-between items-center mb-6 border-b pb-4 flex-wrap gap-2">
                <div className="flex items-center gap-2 flex-wrap">
                  {orderStatuses.map((status, i) => (
                    <div key={i} className="flex items-center gap-2 ">
                      <div
                        className={`h-2 w-8 md:block hidden rounded-full ${
                          getOrderStatusNumber(order.status) >= status.order
                            ? "bg-green-300"
                            : "bg-gray-500"
                        }`}
                      />

                      <div
                        className={`px-3 py-2 rounded-full text-sm font-medium border-2 cursor-pointer text-gray-500 flex items-center gap-2
                      ${
                        getOrderStatusNumber(order.status) >= status.order &&
                        "border-green-300 bg-green-50 text-green-700"
                      }
                      `}
                        onClick={() =>
                          updateOrderStatus(order._id, status.status)
                        }
                      >
                        {getOrderStatusNumber(order.status) >= status.order && (
                          <Check className="h-3 w-3 mr-1" />
                        )}
                        {i + 1}.{" "}
                        {status.status.charAt(0).toUpperCase() +
                          status.status.slice(1)}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex flex-col items-end gap-2">
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      #{order.orderNumber}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {new Date(order.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1 ml-2">
                      {/* Bill Actions Dropdown */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                          >
                            <FileDown className="h-3 w-3 mr-1" />
                            Bill
                            <ChevronDown className="h-3 w-3 ml-1" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Invoice Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => downloadOrderBill(order)}
                          >
                            <FileDown className="h-4 w-4 mr-2" />
                            Download PDF
                          </DropdownMenuItem>
                          {order.userId.email && (
                            <DropdownMenuItem
                              onClick={() => handleEmailInvoice(order)}
                              disabled={emailingInvoices.has(order._id)}
                            >
                              <Mail className="h-4 w-4 mr-2" />
                              {emailingInvoices.has(order._id)
                                ? "Sending..."
                                : `Email to ${order.userId.email}`}
                            </DropdownMenuItem>
                          )}
                          {!order.userId.email && (
                            <DropdownMenuItem disabled>
                              <Mail className="h-4 w-4 mr-2" />
                              Email not available
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>

                      {/* Update Order Button - Only show if payment is not completed */}
                      {order.paymentStatus !== "paid" && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                          onClick={() => {
                            setOrderToUpdate(order);
                            setShowOrderUpdate(true);
                          }}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Update Items
                        </Button>
                      )}

                      {/* Delete Order Button */}
                      <Button
                        size="sm"
                        variant="outline"
                        className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
                        onClick={() => handleDeleteOrder(order._id)}
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </Button>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        {/* <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                        More
                      </Button> */}
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Order Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderManagement(true);
                          }}
                        >
                          <Clock className="h-4 w-4 mr-2" />
                          Advanced Options
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderManagement(true);
                          }}
                        >
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Set Priority
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderManagement(true);
                          }}
                        >
                          <ChefHat className="h-4 w-4 mr-2" />
                          Assign Staff
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>

              {/* Customer & Outlet Info */}
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-gray-800">
                    Customer Details
                  </h4>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4 mr-2" />
                      <p className="text-gray-800 font-medium">
                        {order.userId.name}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 mr-2" />
                      <p className="text-gray-600">{order.userId.phone}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-gray-500">
                    Outlet
                  </h4>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-gray-800">{order.outletId.name}</p>
                    <p className="text-gray-600 text-sm">
                      {order.outletId.address}
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-500">
                  Order Items
                </h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  {order.items.map((item, index) => {
                    const dishKey = `${order._id}-${index}`;
                    const isUpdating = updatingDishes.has(dishKey);

                    return (
                      <div
                        key={index}
                        className={`flex justify-between items-center py-3 ${
                          index !== order.items.length - 1
                            ? "border-b border-gray-200"
                            : ""
                        } ${
                          item.isServed
                            ? "bg-green-50 border-green-200 rounded-md mb-2"
                            : ""
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={item.isServed || false}
                            onCheckedChange={() =>
                              handleDishServingToggle(
                                order._id,
                                index,
                                item.isServed || false
                              )
                            }
                            disabled={isUpdating}
                            className="mt-1"
                          />
                          <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-sm">
                            {item.quantity}x
                          </span>
                          <div className="flex flex-col">
                            <span
                              className={`text-gray-800 ${
                                item.isServed
                                  ? "line-through text-gray-500"
                                  : ""
                              }`}
                            >
                              {item.dishId?.name ||
                                item.dishName ||
                                "Deleted Dish"}
                            </span>
                            {!item.dishId && (
                              <span className="text-xs text-red-500 ml-2">
                                (Dish no longer available)
                              </span>
                            )}
                            {((item.servedQuantity &&
                              item.servedQuantity > 0) ||
                              item.isServed) && (
                              <span className="text-xs text-green-600 flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                {item.servedQuantity &&
                                item.servedQuantity > 0 ? (
                                  <>
                                    Served: {item.servedQuantity}/
                                    {item.quantity}
                                    {item.servedQuantity < item.quantity && (
                                      <span className="text-orange-600">
                                        • {item.quantity - item.servedQuantity}{" "}
                                        pending
                                      </span>
                                    )}
                                  </>
                                ) : (
                                  "Served"
                                )}
                                {item.servedAt && (
                                  <span className="text-gray-500">
                                    •{" "}
                                    {new Date(
                                      item.servedAt
                                    ).toLocaleTimeString()}
                                  </span>
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                        <span className="text-gray-600">
                          ₹
                          {(
                            (item.dishId?.price || item.price || 0) *
                            item.quantity
                          ).toFixed(2)}
                        </span>
                      </div>
                    );
                  })}
                  {/* Total Amount */}
                  <div className="mt-4 pt-3 border-t border-gray-300 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-gray-700">
                        Subtotal
                      </span>
                      <span className="font-semibold text-gray-700">
                        ₹{order.totalAmount.toFixed(2)}
                      </span>
                    </div>

                    {order.couponCode &&
                      order.couponDiscount &&
                      order.couponDiscount > 0 && (
                        <div className="flex justify-between items-center text-green-600">
                          <span className="font-semibold">
                            Coupon Discount ({order.couponCode})
                          </span>
                          <span className="font-semibold">
                            -₹{order.couponDiscount.toFixed(2)}
                          </span>
                        </div>
                      )}

                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-gray-700">
                        Final Amount
                      </span>
                      <span className="text-lg font-bold text-primary">
                        ₹{(order.finalAmount || order.totalAmount).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {order.specialInstructions && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-100 rounded-lg">
                  <h4 className="text-sm font-semibold text-yellow-800 mb-1">
                    Special Instructions
                  </h4>
                  <p className="text-yellow-700">{order.specialInstructions}</p>
                </div>
              )}

              {/* Payment Section */}
              <div className="mt-6 pt-4 border-t border-gray-200 flex justify-between items-center">
                <div>
                  <h4 className="text-sm font-semibold text-gray-500 mb-3">
                    Payment Information
                  </h4>

                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-sm text-gray-600">
                      Payment Status:
                    </span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        order.paymentStatus === "paid"
                          ? "bg-green-100 text-green-800"
                          : order.paymentStatus === "requested"
                          ? "bg-blue-100 text-blue-800"
                          : order.paymentStatus === "failed"
                          ? "bg-red-100 text-red-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {order.paymentStatus?.toUpperCase() || "PENDING"}
                    </span>
                    {order.paymentStatus === "pending" ? (
                      <Button
                        size="sm"
                        variant="outline"
                        className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100 "
                        onClick={() =>
                          updateOrderPaymentStatus(order._id, "paid", "cash")
                        }
                      >
                        <Check className="h-3 w-3 mr-1" /> Mark as Paid
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        variant="outline"
                        className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                        onClick={() =>
                          updateOrderPaymentStatus(order._id, "pending")
                        }
                      >
                        <X className="h-3 w-3 mr-1" /> Mark as Pending
                      </Button>
                    )}
                    <span className="text-sm text-gray-600 ml-4">Method:</span>
                    <span className="text-sm font-medium">
                      {order.paymentMethod === "online"
                        ? "Online Payment"
                        : "Cash"}
                    </span>
                  </div>
                </div>

                {/* Payment Actions */}
                <div className="grid grid-cols-1 gap-4">
                  {/* Show payment request button if payment is not yet requested or failed */}
                  {(!payments[order._id] ||
                    order.paymentStatus === "pending" ||
                    order.paymentStatus === "failed") && (
                    <PaymentRequestButton
                      orderId={order._id.toString()}
                      paymentStatus={order.paymentStatus || "pending"}
                      onPaymentRequested={(payment) =>
                        handlePaymentRequested(order._id, payment)
                      }
                    />
                  )}

                  {/* Show payment link if payment has been requested */}
                </div>
              </div>
              {payments[order._id] && (
                <PaymentLinkDisplay
                  payment={payments[order._id]}
                  isAdmin={true}
                />
              )}
            </div>
          );
        })}
      </div>

      {/* Load More Button */}
      {!useSocketOrders && pagination.hasNext && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={loadMoreOrders}
            disabled={loadingMore}
            className="min-w-32"
          >
            {loadingMore ? (
              <>
                <Clock className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Orders"
            )}
          </Button>
        </div>
      )}

      {/* Order Management Dialog */}
      {selectedOrder && (
        <OrderManagementDialog
          order={selectedOrder}
          staffList={staff}
          open={showOrderManagement}
          onOpenChange={setShowOrderManagement}
          onSuccess={() => {
            // Refresh the orders
            window.location.reload();
          }}
        />
      )}

      {/* Create Order Dialog */}
      <CreateOrderDialog
        open={showCreateOrder}
        onOpenChange={setShowCreateOrder}
        onSuccess={() => {
          // Refresh the orders
          window.location.reload();
        }}
      />

      {/* Admin Order Update Dialog */}
      {orderToUpdate && (
        <AdminOrderUpdateDialog
          order={orderToUpdate}
          isOpen={showOrderUpdate}
          onClose={() => {
            setShowOrderUpdate(false);
            setOrderToUpdate(null);
          }}
          onSuccess={() => {
            // Refresh the orders
            window.location.reload();
          }}
        />
      )}

      <OrderSearch orders={orders} />

      {/* Order Update Notifications */}
      <OrderUpdateNotifications
        onNavigateToOrder={handleNavigateToOrder}
        onRefreshOrders={refreshOrders}
      />
    </div>
  );
};

export default AdminOrdersPage;
